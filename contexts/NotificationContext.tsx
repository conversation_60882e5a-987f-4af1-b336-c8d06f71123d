import React, { createContext, useContext, useState, useCallback, useMemo, ReactNode } from 'react';
import { Notification, NotificationContextType } from '@/types/notification';

// 創建通知上下文
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// 模擬通知數據 - 基於設計文件的示例
const mockNotifications: Notification[] = [
  {
    id: '1',
    type: 'mother_baby_transfer',
    title: 'Mother/Baby Transfer',
    description: 'Mother: <PERSON>, Baby: Girl',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小時前
    status: 'pending',
    priority: 'high',
    initiator: {
      id: 'nurse_001',
      name: '<PERSON>'
    },
    patient: {
      name: '<PERSON>',
      bedNumber: 'A301',
      ward: '5B'
    }
  },
  {
    id: '2',
    type: 'mother_baby_transfer',
    title: 'Mother/Baby Transfer',
    description: 'Mother: <PERSON>, Baby: Girl',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 昨天
    status: 'pending',
    priority: 'high',
    initiator: {
      id: 'nurse_002',
      name: 'Nurse <PERSON>'
    },
    patient: {
      name: '<PERSON>',
      bedNumber: 'B205',
      ward: 'NICU'
    }
  },
  {
    id: '3',
    type: 'medication_administered',
    title: 'Medication Administered',
    description: 'Patient: John Doe - Pain Reliever',
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6小時前
    status: 'confirmed',
    priority: 'medium',
    initiator: {
      id: 'nurse_003',
      name: 'Nurse Wilson'
    },
    patient: {
      name: 'John Doe',
      bedNumber: 'C102',
      ward: 'ICU'
    }
  },
  {
    id: '4',
    type: 'lab_results_available',
    title: 'Lab Results Available',
    description: 'Patient: Jane Smith - Blood Test',
    timestamp: new Date(Date.now() - 18 * 60 * 60 * 1000), // 昨天下午
    status: 'confirmed',
    priority: 'medium',
    initiator: {
      id: 'lab_tech_001',
      name: 'Lab Tech Chen'
    },
    patient: {
      name: 'Jane Smith',
      bedNumber: 'D304',
      ward: 'General'
    }
  },
  {
    id: '5',
    type: 'discharge_approved',
    title: 'Discharge Approved',
    description: 'Patient: Michael Johnson',
    timestamp: new Date(Date.now() - 48 * 60 * 60 * 1000), // 2天前
    status: 'confirmed',
    priority: 'low',
    initiator: {
      id: 'doctor_001',
      name: 'Dr. Lee'
    },
    patient: {
      name: 'Michael Johnson',
      bedNumber: 'E201',
      ward: 'Recovery'
    }
  }
];

/**
 * 通知上下文提供者組件
 * 管理應用中所有通知相關的狀態和操作
 */
export function NotificationProvider({ children }: { children: ReactNode }) {
  // 通知列表狀態
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 計算未讀通知數量 - 使用 useMemo 優化性能
  const unreadCount = useMemo(() => {
    return notifications.filter(notification => notification.status === 'pending').length;
  }, [notifications]);

  // 標記通知為已讀
  const markAsRead = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, status: 'confirmed' as const }
          : notification
      )
    );
  }, []);

  // 標記通知為未讀
  const markAsUnread = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, status: 'pending' as const }
          : notification
      )
    );
  }, []);

  // 刷新通知列表
  const refreshNotifications = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 模擬 API 調用延遲
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 在實際應用中，這裡會調用 API 獲取最新通知
      // const response = await notificationAPI.getNotifications();
      // setNotifications(response.data);
      
      console.log('通知列表已刷新');
    } catch (err) {
      setError('刷新通知失敗');
      console.error('刷新通知錯誤:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 刪除通知
  const deleteNotification = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.filter(notification => notification.id !== notificationId)
    );
  }, []);

  // 標記所有通知為已讀
  const markAllAsRead = useCallback(() => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, status: 'confirmed' as const }))
    );
  }, []);

  // 上下文值 - 使用 useMemo 避免不必要的重新渲染
  const contextValue = useMemo(() => ({
    // 狀態
    notifications,
    unreadCount,
    loading,
    error,
    // 操作
    markAsRead,
    markAsUnread,
    refreshNotifications,
    deleteNotification,
    markAllAsRead,
  }), [
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAsUnread,
    refreshNotifications,
    deleteNotification,
    markAllAsRead,
  ]);

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
}

/**
 * 使用通知上下文的 Hook
 * 提供類型安全的上下文訪問
 */
export function useNotifications(): NotificationContextType {
  const context = useContext(NotificationContext);
  
  if (context === undefined) {
    throw new Error('useNotifications 必須在 NotificationProvider 內部使用');
  }
  
  return context;
}
