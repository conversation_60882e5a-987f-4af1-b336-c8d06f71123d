import React, { createContext, useContext, useState, useCallback, useMemo, ReactNode } from 'react';
import { Notification, NotificationContextType } from '@/types/notification';

// 創建通知上下文
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// 模擬通知數據 - 基於設計文件的示例，包含接收者信息和臨床筆記
const mockNotifications: Notification[] = [
  {
    id: '1',
    type: 'mother_baby_transfer',
    title: 'Mother/Baby Transfer',
    description: 'Mother: <PERSON>, Baby: Girl',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小時前
    status: 'pending',
    priority: 'high',
    initiator: {
      id: 'nurse_001',
      name: 'Nurse <PERSON>'
    },
    patient: {
      name: '<PERSON>',
      bedNumber: 'A301',
      ward: '5B'
    },
    clinicalNotes: 'Patient requires immediate transfer to specialized care unit. Please ensure all medical equipment is ready.',
    recipients: [
      {
        id: 'nurse_003',
        name: 'Nurse <PERSON>',
        status: 'confirmed',
        phoneNumber: '******-0123',
        isCurrentUser: false,
        lastUpdated: new Date(Date.now() - 30 * 60 * 1000)
      },
      {
        id: 'nurse_004',
        name: 'Nurse <PERSON> Early',
        status: 'received',
        phoneNumber: '******-0124',
        isCurrentUser: true,
        lastUpdated: new Date(Date.now() - 10 * 60 * 1000)
      }
    ],
    currentUserId: 'nurse_004'
  },
  {
    id: '2',
    type: 'mother_baby_transfer',
    title: 'Mother/Baby Transfer',
    description: 'Mother: Sophia Brown, Baby: Girl',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 昨天
    status: 'pending',
    priority: 'high',
    initiator: {
      id: 'dr_grace_hall',
      name: 'Grace Hall',
      avatar: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBnewFcf7Z1ViEI-DOVLtEqAJfV2pZs34Get9JUj6algN83TwvSjmOdsJbz9tnPCm5B8tBgaJtTL5fjw-j_n-j8SeKCqIVnAhqZSnq1Wrtd59C9ESprBbkZ3Ks34Pew-YLbnzQo_EydE_Da8Yo6VzWut-2LaaJ2SUhFB-nMC6f9i1I16JJtLm1noYbh6NUSgIDGHo5kzd5EAg1cBgy_kW7sxayCrL-0BDfA8tSuMgU3u4uMgm8ifrTwtwlpkCXmWSg4gic4mhZpwvE'
    },
    patient: {
      name: 'Sophia Brown',
      bedNumber: 'B205',
      ward: 'D05'
    },
    clinicalNotes: 'This is an important notification regarding the transfer of Sophia Brown and her baby girl. Please review the details and acknowledge receipt.',
    recipients: [
      {
        id: 'nurse_ian_johns',
        name: 'Nurse Ian Johns',
        status: 'confirmed',
        phoneNumber: '******-0125',
        isCurrentUser: false,
        lastUpdated: new Date(Date.now() - 2 * 60 * 60 * 1000)
      },
      {
        id: 'nurse_betty_early',
        name: 'You (Nurse Betty Early)',
        status: 'received',
        phoneNumber: '******-0126',
        isCurrentUser: true,
        avatar: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBChJV-xTPWOvyUt46X6mpENnob0sPNvXtcbXkfHD2NAEvwZ7xeBM7L0jskltmKPbnND_8ELJLcfZ2I4r4vMGbZgIJgtwtkfzuNUT5nDhBO4aHEzA8_YOkHj31VJMeJAPrNUYKLQ9r0q-JR_8McH6-Ko_4M_-wXjHY6WWufNmiain7SDFoFKhOMClkhero7EbeZQm9SoRtNb4RnV8Vm1ea2dqfOx6FrNBOxBeDBv9AsDF42lQ6XvElpVSQpkO6f0s7mwLSqd6XIBag',
        lastUpdated: new Date(Date.now() - 30 * 60 * 1000)
      },
      {
        id: 'pending_confirmation',
        name: 'Pending Confirmation',
        status: 'pending',
        phoneNumber: '******-0127',
        isCurrentUser: false,
        lastUpdated: new Date()
      }
    ],
    currentUserId: 'nurse_betty_early'
  },
  {
    id: '3',
    type: 'medication_administered',
    title: 'Medication Administered',
    description: 'Patient: John Doe - Pain Reliever',
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6小時前
    status: 'confirmed',
    priority: 'medium',
    initiator: {
      id: 'nurse_003',
      name: 'Nurse Wilson'
    },
    patient: {
      name: 'John Doe',
      bedNumber: 'C102',
      ward: 'ICU'
    },
    clinicalNotes: 'Pain medication administered as prescribed. Patient reported significant improvement in comfort level.',
    recipients: [
      {
        id: 'dr_smith',
        name: 'Dr. Smith',
        status: 'confirmed',
        phoneNumber: '******-0128',
        isCurrentUser: false,
        lastUpdated: new Date(Date.now() - 4 * 60 * 60 * 1000)
      }
    ],
    currentUserId: 'nurse_betty_early'
  },
  {
    id: '4',
    type: 'lab_results_available',
    title: 'Lab Results Available',
    description: 'Patient: Jane Smith - Blood Test',
    timestamp: new Date(Date.now() - 18 * 60 * 60 * 1000), // 昨天下午
    status: 'confirmed',
    priority: 'medium',
    initiator: {
      id: 'lab_tech_001',
      name: 'Lab Tech Chen'
    },
    patient: {
      name: 'Jane Smith',
      bedNumber: 'D304',
      ward: 'General'
    },
    clinicalNotes: 'Blood test results are now available for review. All values are within normal ranges.',
    recipients: [
      {
        id: 'dr_johnson',
        name: 'Dr. Johnson',
        status: 'confirmed',
        phoneNumber: '******-0129',
        isCurrentUser: false,
        lastUpdated: new Date(Date.now() - 16 * 60 * 60 * 1000)
      }
    ],
    currentUserId: 'nurse_betty_early'
  },
  {
    id: '5',
    type: 'discharge_approved',
    title: 'Discharge Approved',
    description: 'Patient: Michael Johnson',
    timestamp: new Date(Date.now() - 48 * 60 * 60 * 1000), // 2天前
    status: 'confirmed',
    priority: 'low',
    initiator: {
      id: 'doctor_001',
      name: 'Dr. Lee'
    },
    patient: {
      name: 'Michael Johnson',
      bedNumber: 'E201',
      ward: 'Recovery'
    },
    clinicalNotes: 'Patient has recovered well and is ready for discharge. Please coordinate with family for pickup.',
    recipients: [
      {
        id: 'nurse_coordinator',
        name: 'Nurse Coordinator',
        status: 'confirmed',
        phoneNumber: '******-0130',
        isCurrentUser: false,
        lastUpdated: new Date(Date.now() - 46 * 60 * 60 * 1000)
      }
    ],
    currentUserId: 'nurse_betty_early'
  }
];

/**
 * 通知上下文提供者組件
 * 管理應用中所有通知相關的狀態和操作
 */
export function NotificationProvider({ children }: { children: ReactNode }) {
  // 通知列表狀態
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 計算未讀通知數量 - 使用 useMemo 優化性能
  const unreadCount = useMemo(() => {
    return notifications.filter(notification => notification.status === 'pending').length;
  }, [notifications]);

  // 標記通知為已讀
  const markAsRead = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, status: 'confirmed' as const }
          : notification
      )
    );
  }, []);

  // 標記通知為未讀
  const markAsUnread = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, status: 'pending' as const }
          : notification
      )
    );
  }, []);

  // 刷新通知列表
  const refreshNotifications = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 模擬 API 調用延遲
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 在實際應用中，這裡會調用 API 獲取最新通知
      // const response = await notificationAPI.getNotifications();
      // setNotifications(response.data);
      
      console.log('通知列表已刷新');
    } catch (err) {
      setError('刷新通知失敗');
      console.error('刷新通知錯誤:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 刪除通知
  const deleteNotification = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.filter(notification => notification.id !== notificationId)
    );
  }, []);

  // 標記所有通知為已讀
  const markAllAsRead = useCallback(() => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, status: 'confirmed' as const }))
    );
  }, []);

  // 確認通知（用於接收者）
  const acknowledgeNotification = useCallback((notificationId: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, status: 'confirmed' as const }
          : notification
      )
    );
  }, []);

  // 取消通知（用於發送者）
  const cancelNotification = useCallback((notificationId: string) => {
    setNotifications(prev =>
      prev.filter(notification => notification.id !== notificationId)
    );
  }, []);

  // 獲取通知詳情
  const getNotificationById = useCallback((notificationId: string) => {
    return notifications.find(notification => notification.id === notificationId);
  }, [notifications]);

  // 上下文值 - 使用 useMemo 避免不必要的重新渲染
  const contextValue = useMemo(() => ({
    // 狀態
    notifications,
    unreadCount,
    loading,
    error,
    // 操作
    markAsRead,
    markAsUnread,
    refreshNotifications,
    deleteNotification,
    markAllAsRead,
    acknowledgeNotification,
    cancelNotification,
    getNotificationById,
  }), [
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAsUnread,
    refreshNotifications,
    deleteNotification,
    markAllAsRead,
    acknowledgeNotification,
    cancelNotification,
    getNotificationById,
  ]);

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
}

/**
 * 使用通知上下文的 Hook
 * 提供類型安全的上下文訪問
 */
export function useNotifications(): NotificationContextType {
  const context = useContext(NotificationContext);
  
  if (context === undefined) {
    throw new Error('useNotifications 必須在 NotificationProvider 內部使用');
  }
  
  return context;
}
