import { MaterialIcons } from '@expo/vector-icons';
import React, { useCallback, useMemo } from 'react';
import { Platform, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Avatar, Chip, Surface, Text, useTheme } from 'react-native-paper';

import { RecipientInfo } from '@/types/notification';
import { SwipeToCall } from './SwipeToCall';

interface PersonListItemProps {
  /** 人員信息 */
  person: RecipientInfo;
  /** 是否為發送者 */
  isSender?: boolean;
  /** 發送者信息（當 isSender 為 true 時使用） */
  senderInfo?: {
    id: string;
    name: string;
    avatar?: string;
  };
  /** 點擊回調函數 */
  onPress?: (person: RecipientInfo) => void;
}

/**
 * 人員列表項組件
 * 顯示發送者或接收者信息，支持滑動拨號功能
 */
export function PersonListItem({
  person,
  isSender = false,
  senderInfo,
  onPress
}: PersonListItemProps) {
  const theme = useTheme();

  // 獲取顯示的人員信息 - 使用 useMemo 優化性能
  const displayPerson = useMemo(() => {
    return isSender && senderInfo ? {
      ...person,
      id: senderInfo.id,
      name: senderInfo.name,
      avatar: senderInfo.avatar,
    } : person;
  }, [person, isSender, senderInfo]);

  // 獲取狀態配置
  const getStatusConfig = useCallback(() => {
    if (isSender) {
      return {
        chipColor: theme.colors.primary,
        chipBackgroundColor: theme.dark ? 'rgba(103, 80, 164, 0.2)' : '#E8EAF6',
        statusText: 'Sender',
        icon: 'send' as const,
      };
    }

    switch (person.status) {
      case 'confirmed':
        return {
          chipColor: theme.dark ? '#22C55E' : '#16A34A',
          chipBackgroundColor: theme.dark ? 'rgba(34, 197, 94, 0.2)' : '#DCFCE7',
          statusText: 'Confirmed',
          icon: 'check-circle' as const,
        };
      case 'received':
        return {
          chipColor: theme.dark ? '#F59E0B' : '#D97706',
          chipBackgroundColor: theme.dark ? 'rgba(251, 191, 36, 0.2)' : '#FEF3C7',
          statusText: 'Received',
          icon: 'hourglass-empty' as const,
        };
      case 'pending':
      default:
        return {
          chipColor: theme.dark ? '#EF4444' : '#DC2626',
          chipBackgroundColor: theme.dark ? 'rgba(239, 68, 68, 0.2)' : '#FEE2E2',
          statusText: 'Pending',
          icon: 'schedule' as const,
        };
    }
  }, [person.status, isSender, theme]);

  // 處理點擊事件
  const handlePress = useCallback(() => {
    onPress?.(person);
  }, [person, onPress]);

  // 處理滑動事件
  const handleSwipe = useCallback(() => {
    console.log('滑動拨號:', displayPerson.name, displayPerson.phoneNumber);
  }, [displayPerson]);

  const statusConfig = getStatusConfig();

  // 渲染頭像
  const renderAvatar = () => {
    if (displayPerson.avatar) {
      return (
        <Avatar.Image
          size={40}
          source={{ uri: displayPerson.avatar }}
        />
      );
    } else {
      return (
        <Avatar.Icon
          size={40}
          icon="person"
          style={{ backgroundColor: theme.colors.surfaceVariant }}
        />
      );
    }
  };

  const content = (
    <Surface
      style={[
        styles.surface,
        { backgroundColor: theme.colors.surface }
      ]}
      elevation={1}
    >
      <TouchableOpacity
        style={styles.touchable}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <View style={styles.contentContainer}>
          {/* 左側頭像和信息 */}
          <View style={styles.leftContent}>
            {renderAvatar()}
            <View style={styles.textContainer}>
              <Text
                style={[
                  styles.name,
                  {
                    color: theme.colors.onSurface,
                    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
                  }
                ]}
                numberOfLines={1}
              >
                {displayPerson.name}
              </Text>
            </View>
          </View>

          {/* 右側狀態和箭頭 */}
          <View style={styles.rightContent}>
            <Chip
              icon={() => (
                <MaterialIcons
                  name={statusConfig.icon}
                  size={16}
                  color={statusConfig.chipColor}
                />
              )}
              style={[
                styles.statusChip,
                { backgroundColor: statusConfig.chipBackgroundColor }
              ]}
              textStyle={[
                styles.statusText,
                { color: statusConfig.chipColor }
              ]}
              compact
            >
              {statusConfig.statusText}
            </Chip>

            <MaterialIcons
              name="chevron-right"
              size={20}
              color={theme.colors.onSurfaceVariant}
              style={styles.chevronIcon}
            />
          </View>
        </View>
      </TouchableOpacity>
    </Surface>
  );

  // 只有非發送者且有電話號碼的項目才支持滑動拨號
  const enableSwipe = !isSender && !!displayPerson.phoneNumber;

  return (
    <View style={styles.container}>
      <SwipeToCall
        phoneNumber={displayPerson.phoneNumber}
        enabled={enableSwipe}
        onSwipe={handleSwipe}
      >
        {content}
      </SwipeToCall>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  surface: {
    borderRadius: 8,
    // backgroundColor 通過主題動態設置
  },
  touchable: {
    borderRadius: 8,
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  leftContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  textContainer: {
    marginLeft: 12,
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    // color 通過主題動態設置
  },
  rightContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  statusChip: {
    marginBottom: 4,
    // backgroundColor 通過 statusConfig 動態設置
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    // color 通過 statusConfig 動態設置
  },
  chevronIcon: {
    // color 通過主題動態設置
  },
});
