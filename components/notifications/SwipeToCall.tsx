import * as Linking from 'expo-linking';
import React, { useRef } from 'react';
import { Animated, StyleSheet, View } from 'react-native';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import { IconButton, useTheme } from 'react-native-paper';

interface SwipeToCallProps {
  /** 子組件 */
  children: React.ReactNode;
  /** 電話號碼 */
  phoneNumber?: string;
  /** 是否啟用滑動功能 */
  enabled?: boolean;
  /** 滑動回調函數 */
  onSwipe?: () => void;
}

/**
 * 滑動拨號組件
 * 支持向右滑動觸發拨打電話功能
 */
export function SwipeToCall({
  children,
  phoneNumber,
  enabled = true,
  onSwipe
}: SwipeToCallProps) {
  const theme = useTheme();
  const translateX = useRef(new Animated.Value(0)).current;

  // 處理手勢狀態變化
  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    { useNativeDriver: false }
  );

  // 處理手勢結束
  const onHandlerStateChange = (event: any) => {
    if (!enabled) return;

    const { state, translationX } = event.nativeEvent;

    if (state === State.END) {
      const threshold = -80; // 滑動閾值
      
      if (translationX < threshold) {
        // 觸發滑動狀態
        Animated.timing(translateX, {
          toValue: threshold,
          duration: 200,
          useNativeDriver: false,
        }).start();

        onSwipe?.();
      } else {
        // 重置位置
        Animated.timing(translateX, {
          toValue: 0,
          duration: 200,
          useNativeDriver: false,
        }).start();
      }
    }
  };

  // 處理電話拨打
  const handleCall = async () => {
    if (!phoneNumber) return;

    try {
      const phoneUrl = `tel:${phoneNumber}`;
      const canOpen = await Linking.canOpenURL(phoneUrl);
      
      if (canOpen) {
        await Linking.openURL(phoneUrl);
      } else {
        console.warn('無法打開電話應用');
      }
    } catch (error) {
      console.error('拨打電話時發生錯誤:', error);
    }
  };

  if (!enabled) {
    return <View style={styles.container}>{children}</View>;
  }

  return (
    <View style={styles.container}>
      {/* 滑動操作按鈕 */}
      <View style={[
        styles.swipeActions,
        { backgroundColor: theme.colors.primary }
      ]}>
        <IconButton
          icon="phone"
          iconColor={theme.colors.onPrimary}
          size={24}
          onPress={handleCall}
        />
      </View>

      {/* 可滑動的內容 */}
      <PanGestureHandler
        onGestureEvent={onGestureEvent}
        onHandlerStateChange={onHandlerStateChange}
        activeOffsetX={[-10, 10]}
        failOffsetY={[-5, 5]}
      >
        <Animated.View
          style={[
            styles.swipeContent,
            {
              transform: [{ translateX }],
              backgroundColor: theme.colors.surface,
            }
          ]}
        >
          {children}
        </Animated.View>
      </PanGestureHandler>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
    borderRadius: 8,
  },
  swipeActions: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    width: 80,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  swipeContent: {
    // backgroundColor 通過主題動態設置
    zIndex: 2,
  },
});
