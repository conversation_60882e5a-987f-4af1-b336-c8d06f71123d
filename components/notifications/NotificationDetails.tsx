import { MaterialIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useCallback, useMemo } from 'react';
import { Platform, ScrollView, StyleSheet, View } from 'react-native';
import { Appbar, Button, Card, Text, useTheme } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useNotifications } from '@/contexts/NotificationContext';
import { RecipientInfo } from '@/types/notification';
import { PersonListItem } from './PersonListItem';

interface NotificationDetailsProps {
  /** 通知ID */
  notificationId: string;
}

/**
 * 通知詳情頁面組件
 * 顯示通知的完整信息，包括發送者、接收者列表和操作按鈕
 */
export function NotificationDetails({ notificationId }: NotificationDetailsProps) {
  const theme = useTheme();
  const { getNotificationById, acknowledgeNotification, cancelNotification } = useNotifications();

  // 獲取通知詳情
  const notification = useMemo(() => {
    return getNotificationById(notificationId);
  }, [notificationId, getNotificationById]);

  // 格式化時間顯示
  const formatTimestamp = useCallback((timestamp: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      return 'Today, ' + timestamp.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      });
    } else if (diffInHours < 48) {
      return 'Yesterday, ' + timestamp.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      });
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} days ago, ` + timestamp.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      });
    }
  }, []);

  // 判斷當前用戶角色
  const getUserRole = useCallback(() => {
    if (!notification) return 'recipient';
    
    // 檢查是否為發送者
    if (notification.initiator?.id === notification.currentUserId) {
      return 'sender';
    }
    
    return 'recipient';
  }, [notification]);

  // 處理確認通知
  const handleAcknowledge = useCallback(() => {
    if (!notification) return;
    
    acknowledgeNotification(notification.id);
    router.back();
  }, [notification, acknowledgeNotification]);

  // 處理取消通知
  const handleCancel = useCallback(() => {
    if (!notification) return;
    
    cancelNotification(notification.id);
    router.back();
  }, [notification, cancelNotification]);

  // 處理返回
  const handleBack = useCallback(() => {
    router.back();
  }, []);

  // 處理人員點擊
  const handlePersonPress = useCallback((person: RecipientInfo) => {
    console.log('人員被點擊:', person.name);
    // 這裡可以添加更多交互邏輯，比如顯示人員詳情
  }, []);

  if (!notification) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Appbar.Header style={{ backgroundColor: theme.colors.surface }}>
          <Appbar.BackAction onPress={handleBack} />
          <Appbar.Content title="Notification Details" />
        </Appbar.Header>
        <View style={styles.errorContainer}>
          <Text style={{ color: theme.colors.onSurface }}>通知未找到</Text>
        </View>
      </SafeAreaView>
    );
  }

  const userRole = getUserRole();
  const isCurrentUserRecipient = userRole === 'recipient';

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        {/* 頂部標題欄 */}
        <Appbar.Header style={[styles.header, { backgroundColor: theme.colors.primary }]}>
          <Appbar.BackAction 
            onPress={handleBack}
            iconColor={theme.colors.onPrimary}
          />
          <Appbar.Content
            title="Notification Details"
            titleStyle={[
              styles.headerTitle,
              {
                color: theme.colors.onPrimary,
                fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
              }
            ]}
          />
        </Appbar.Header>

        {/* 主要內容區域 */}
        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.contentContainer}
        >
          {/* 通知信息區域 */}
          <View style={styles.notificationInfo}>
            <Text
              style={[
                styles.title,
                {
                  color: theme.colors.onSurface,
                  fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
                }
              ]}
            >
              {notification.title}
            </Text>
            
            {notification.patient && (
              <View style={styles.patientInfo}>
                <Text style={[styles.patientLabel, { color: theme.colors.onSurface }]}>
                  Mother Initial: <Text style={styles.patientValue}>{notification.patient.name}</Text>
                </Text>
                <Text style={[styles.patientLabel, { color: theme.colors.onSurface }]}>
                  Designated Ward: <Text style={styles.patientValue}>{notification.patient.ward}</Text>
                </Text>
              </View>
            )}
            
            <Text
              style={[
                styles.timestamp,
                {
                  color: theme.colors.onSurfaceVariant,
                  fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
                }
              ]}
            >
              {formatTimestamp(notification.timestamp)}
            </Text>
          </View>

          {/* 臨床筆記區域 */}
          {notification.clinicalNotes && (
            <Card style={[styles.clinicalNotesCard, { backgroundColor: theme.colors.surfaceVariant }]}>
              <Card.Content>
                <Text
                  style={[
                    styles.clinicalNotesTitle,
                    {
                      color: theme.colors.onSurfaceVariant,
                      fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
                    }
                  ]}
                >
                  Clinical Notes:
                </Text>
                <Text
                  style={[
                    styles.clinicalNotesContent,
                    {
                      color: theme.colors.onSurfaceVariant,
                      fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
                    }
                  ]}
                >
                  {notification.clinicalNotes}
                </Text>
              </Card.Content>
            </Card>
          )}

          {/* 發送者和接收者區域 */}
          <View style={styles.peopleSection}>
            <Text
              style={[
                styles.sectionTitle,
                {
                  color: theme.colors.onSurface,
                  fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
                }
              ]}
            >
              Sender & Other Recipients
            </Text>

            {/* 發送者 */}
            {notification.initiator && (
              <PersonListItem
                person={{
                  id: notification.initiator.id,
                  name: notification.initiator.name,
                  status: 'confirmed',
                }}
                isSender={true}
                senderInfo={notification.initiator}
                onPress={handlePersonPress}
              />
            )}

            {/* 接收者列表 */}
            {notification.recipients?.map((recipient) => (
              <PersonListItem
                key={recipient.id}
                person={recipient}
                onPress={handlePersonPress}
              />
            ))}
          </View>
        </ScrollView>

        {/* 底部操作按鈕 */}
        <View style={[styles.footer, { backgroundColor: theme.colors.surface }]}>
          {isCurrentUserRecipient ? (
            <Button
              mode="contained"
              onPress={handleAcknowledge}
              style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
              contentStyle={styles.buttonContent}
              labelStyle={[
                styles.buttonLabel,
                {
                  color: theme.colors.onPrimary,
                  fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
                }
              ]}
              icon={() => (
                <MaterialIcons
                  name="check-circle-outline"
                  size={20}
                  color={theme.colors.onPrimary}
                />
              )}
            >
              Acknowledge
            </Button>
          ) : (
            <Button
              mode="contained"
              onPress={handleCancel}
              style={[styles.actionButton, { backgroundColor: theme.colors.error }]}
              contentStyle={styles.buttonContent}
              labelStyle={[
                styles.buttonLabel,
                {
                  color: theme.colors.onError,
                  fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
                }
              ]}
              icon={() => (
                <MaterialIcons
                  name="cancel"
                  size={20}
                  color={theme.colors.onError}
                />
              )}
            >
              Cancel Notification
            </Button>
          )}
        </View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor 通過主題動態設置
  },
  safeArea: {
    flex: 1,
  },
  header: {
    // backgroundColor 通過主題動態設置
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    // color 通過主題動態設置
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  notificationInfo: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    // color 通過主題動態設置
  },
  patientInfo: {
    marginBottom: 8,
  },
  patientLabel: {
    fontSize: 16,
    marginBottom: 4,
    // color 通過主題動態設置
  },
  patientValue: {
    fontWeight: '600',
  },
  timestamp: {
    fontSize: 14,
    // color 通過主題動態設置
  },
  clinicalNotesCard: {
    marginBottom: 24,
    // backgroundColor 通過主題動態設置
  },
  clinicalNotesTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    // color 通過主題動態設置
  },
  clinicalNotesContent: {
    fontSize: 16,
    lineHeight: 24,
    // color 通過主題動態設置
  },
  peopleSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    // color 通過主題動態設置
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.12)',
    // backgroundColor 通過主題動態設置
  },
  actionButton: {
    // backgroundColor 通過主題動態設置
  },
  buttonContent: {
    height: 48,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: '600',
    // color 通過主題動態設置
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
