// 通知相關的 TypeScript 類型定義

/**
 * 通知狀態枚舉
 */
export type NotificationStatus = 'pending' | 'confirmed';

/**
 * 通知類型枚舉
 */
export type NotificationType = 
  | 'mother_baby_transfer'
  | 'medication_administered' 
  | 'lab_results_available'
  | 'discharge_approved';

/**
 * 通知優先級枚舉
 */
export type NotificationPriority = 'high' | 'medium' | 'low';

/**
 * 通知數據接口
 */
export interface Notification {
  /** 通知唯一標識符 */
  id: string;
  
  /** 通知類型 */
  type: NotificationType;
  
  /** 通知標題 */
  title: string;
  
  /** 通知描述/詳細信息 */
  description: string;
  
  /** 通知創建時間戳 */
  timestamp: Date;
  
  /** 通知狀態 */
  status: NotificationStatus;
  
  /** 通知優先級 */
  priority: NotificationPriority;
  
  /** 發起人信息（可選） */
  initiator?: {
    id: string;
    name: string;
  };
  
  /** 相關患者信息（可選） */
  patient?: {
    name: string;
    bedNumber?: string;
    ward?: string;
  };
}

/**
 * 通知分組接口
 */
export interface NotificationGroup {
  /** 分組標題 */
  title: string;
  
  /** 分組中的通知列表 */
  notifications: Notification[];
  
  /** 分組狀態 */
  status: NotificationStatus;
  
  /** 是否展開顯示 */
  expanded?: boolean;
}

/**
 * 通知上下文狀態接口
 */
export interface NotificationContextState {
  /** 所有通知列表 */
  notifications: Notification[];
  
  /** 未讀通知數量 */
  unreadCount: number;
  
  /** 加載狀態 */
  loading: boolean;
  
  /** 錯誤信息 */
  error: string | null;
}

/**
 * 通知上下文操作接口
 */
export interface NotificationContextActions {
  /** 標記通知為已讀 */
  markAsRead: (notificationId: string) => void;
  
  /** 標記通知為未讀 */
  markAsUnread: (notificationId: string) => void;
  
  /** 刷新通知列表 */
  refreshNotifications: () => Promise<void>;
  
  /** 刪除通知 */
  deleteNotification: (notificationId: string) => void;
  
  /** 標記所有通知為已讀 */
  markAllAsRead: () => void;
}

/**
 * 通知上下文完整接口
 */
export interface NotificationContextType extends NotificationContextState, NotificationContextActions {}
