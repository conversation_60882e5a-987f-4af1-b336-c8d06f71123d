import { MaterialIcons } from '@expo/vector-icons';
import React, { useCallback, useMemo } from 'react';
import { Platform, RefreshControl, ScrollView, StyleSheet, View } from 'react-native';
import { Appbar, Text, useTheme } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

import { NotificationSection } from '@/components/notifications/NotificationSection';
import { useNotifications } from '@/contexts/NotificationContext';
import { Notification } from '@/types/notification';

/**
 * 通知頁面組件
 * 顯示所有通知，按狀態分組（Pending/Confirmed）
 */
export default function NotificationsScreen() {
  // 獲取當前主題以支持 Dark Mode
  const theme = useTheme();

  // 獲取通知上下文
  const {
    notifications,
    loading,
    refreshNotifications,
    // markAllAsRead
  } = useNotifications();

  // 按狀態分組通知 - 使用 useMemo 優化性能
  const groupedNotifications = useMemo(() => {
    const pending = notifications.filter(n => n.status === 'pending');
    const confirmed = notifications.filter(n => n.status === 'confirmed');

    return {
      pending: pending.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()),
      confirmed: confirmed.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    };
  }, [notifications]);

  // 處理通知點擊事件
  const handleNotificationPress = useCallback((notification: Notification) => {
    console.log('通知被點擊:', notification.title);
    // TODO: 導航到通知詳情頁面或執行其他操作
  }, []);

  // 處理下拉刷新
  const handleRefresh = useCallback(() => {
    refreshNotifications();
  }, [refreshNotifications]);

  // 處理標記所有為已讀
  // const handleMarkAllAsRead = useCallback(() => {
  //   markAllAsRead();
  // }, [markAllAsRead]);

  // 動態樣式 - 使用 useMemo 優化性能
  const dynamicStyles = useMemo(() => ({
    container: { backgroundColor: theme.colors.background },
    header: { backgroundColor: theme.colors.surface },
    headerTitle: { color: theme.colors.onSurface },
    content: { backgroundColor: theme.colors.background },
  }), [theme]);

  return (
    <View style={[styles.container, dynamicStyles.container]}>
      <SafeAreaView style={styles.safeArea} edges={['top', 'left', 'right']}>
        {/* 頂部標題欄 */}
        <Appbar.Header style={[styles.header, dynamicStyles.header]} statusBarHeight={0}>
          <Appbar.Content
            title="Notifications"
            titleStyle={[
              styles.headerTitle,
              {
                color: theme.colors.onSurface,
                fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
              }
            ]}
          />

          {/* 標記所有為已讀按鈕 */}
          {/* {groupedNotifications.pending.length > 0 && (
            <Appbar.Action
              icon="done-all"
              onPress={handleMarkAllAsRead}
              iconColor={theme.colors.primary}
            />
          )} */}
        </Appbar.Header>

        {/* 主要內容區域 */}
        <ScrollView
          style={[styles.content, dynamicStyles.content]}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
        >
          {/* 空狀態顯示 */}
          {notifications.length === 0 ? (
            <View style={styles.emptyContainer}>
              <MaterialIcons
                name="notifications-none"
                size={64}
                color={theme.colors.onSurfaceVariant}
                style={styles.emptyIcon}
              />
              <Text style={[
                styles.emptyTitle,
                {
                  color: theme.colors.onSurface,
                  fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
                }
              ]}>
                No Notifications
              </Text>
              <Text style={[
                styles.emptyDescription,
                {
                  color: theme.colors.onSurfaceVariant,
                  fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
                }
              ]}>
                You&apos;re all caught up! New notifications will appear here.
              </Text>
            </View>
          ) : (
            <>
              {/* Pending Notifications 分組 */}
              <NotificationSection
                title="Pending Notifications"
                notifications={groupedNotifications.pending}
                defaultExpanded={true}
                onNotificationPress={handleNotificationPress}
              />

              {/* Confirmed Notifications 分組 */}
              <NotificationSection
                title="Confirmed Notifications"
                notifications={groupedNotifications.confirmed}
                defaultExpanded={true}
                onNotificationPress={handleNotificationPress}
              />
            </>
          )}
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

// 樣式定義 - 遵循 Material Design 原則，使用主題顏色支持 Dark Mode
const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor 通過 theme.colors.background 動態設置
  },
  safeArea: {
    flex: 1,
  },
  header: {
    // backgroundColor 通過 theme.colors.surface 動態設置
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    height: 64,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    // color 通過 theme.colors.onSurface 動態設置
  },
  content: {
    flex: 1,
    paddingTop: 16,
    // backgroundColor 通過 theme.colors.background 動態設置
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyIcon: {
    marginBottom: 16,
    opacity: 0.6,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
    // color 通過 theme.colors.onSurface 動態設置
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    // color 通過 theme.colors.onSurfaceVariant 動態設置
  },
});
