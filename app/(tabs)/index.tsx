import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useCallback, useState, useMemo } from 'react';
import { InteractionManager, Platform, Text as RNText, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import {
  Appbar,
  Avatar,
  Card,
  Text,
  useTheme
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import EditGroupModal from '../../components/EditGroupModal';

// 主頁面組件 - 實現 CareComms 應用的主界面
export default function HomeScreen() {
  // 獲取當前主題以支持 Dark Mode
  const theme = useTheme();

  // Modal 狀態管理
  const [showEditGroupModal, setShowEditGroupModal] = useState(false);

  // 處理卡片點擊事件
  const handleCardPress = useCallback((cardType: string) => {
    console.log(`${cardType} card pressed`);
    if (cardType === 'EditGroup') {
      setShowEditGroupModal(true);
    }
    // TODO: 實現其他導航邏輯
  }, []);

  // 處理群組保存
  const handleGroupSave = useCallback((groupData: { name: string; memberIds: string[] }) => {
    console.log('Group saved:', groupData);
    // TODO: 實現群組保存邏輯
  }, []);

  // 處理群組刪除
  const handleGroupDelete = useCallback(() => {
    console.log('Group deleted');
    // TODO: 實現群組刪除邏輯
  }, []);

  // 處理快速操作按鈕點擊
  const handleQuickAction = useCallback((actionType: string) => {
    console.log(`${actionType} action pressed`);
    // TODO: 實現相應的功能
  }, []);

  // Modal 的 onDismiss 回調
  const handleModalDismiss = useCallback(() => {
    // 使用 InteractionManager 確保在動畫或其他交互完成後執行狀態更新
    // 這有助於避免因狀態更新過早而中斷 Modal 的退出動畫
    InteractionManager.runAfterInteractions(() => {
      setShowEditGroupModal(false);
    });
    // setShowEditGroupModal(false);
  }, []);

  // 使用 useMemo 優化動態樣式，避免每次渲染重新創建對象
  const dynamicStyles = useMemo(() => ({
    container: { backgroundColor: theme.colors.background },
    statusBarBackground: { backgroundColor: theme.colors.primary },
    header: { backgroundColor: theme.colors.primary },
    appTitle: { color: theme.colors.onPrimary },
    cardText: { color: theme.colors.onSurface },
    quickActionsTitle: { color: theme.colors.onSurface },
    profileCard: { backgroundColor: theme.colors.surfaceVariant },
    editGroupCard: { backgroundColor: theme.colors.surfaceVariant },
    quickActionsCard: { backgroundColor: theme.colors.surfaceVariant },
    primaryIconContainer: { backgroundColor: theme.colors.primaryContainer },
    secondaryIconContainer: { backgroundColor: theme.colors.secondaryContainer },
    quickActionsDivider: { backgroundColor: theme.colors.outline },
  }), [theme]);


  return (
    <View style={[styles.container, dynamicStyles.container]}>
      {/* 狀態欄背景 - 優化高度減少不必要留白 */}
      <View style={[styles.statusBarBackground, dynamicStyles.statusBarBackground]} />
      <SafeAreaView style={styles.safeArea} edges={['bottom', 'left', 'right']}>
        {/* 頂部標題欄 */}
        <Appbar.Header style={[styles.header, { backgroundColor: theme.colors.primary }]}>
          <View style={styles.headerContent}>
            <View style={styles.logoContainer}>
              <Avatar.Image
                size={40}
                source={{ uri: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCFiSh23snutDa79cLr4-HtIJZ6A4tk8Wbqhy7HOW1-uH8cgVMVxnK0VCXWFNtMYLIvT9eYoiGhg97iHB5uFNyUqqAjgEaNGjpxIxUw72XXJ_sxHuiCgdTmxqss-M6poAh1cHmu4Ynndc2REpshiKY2UvnfNpo6fecP4ylaRIZfd-6v3sGqgo0wvzh4VLFbJVn9D1qHBoGJoz2FSzEaYzYNJ2CwGykhxOzfIVyYVzz0xnedvU3ZyCePOdzobyJtoz8mjPsEsqwd4Es' }}
                style={styles.logo}
              />
              <Text style={[styles.appTitle, {
                fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
                fontWeight: 'bold',
                color: theme.colors.onPrimary,
              }]}>
                CareComms
              </Text>
            </View>
          </View>
        </Appbar.Header>

        {/* 主要內容區域 */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* 功能卡片網格 */}
          <View style={styles.cardGrid}>
            {/* Profile 卡片 */}
            <TouchableOpacity
              style={styles.cardWrapper}
              onPress={() => handleCardPress('Profile')}
              activeOpacity={0.7}
            >
              <Card style={[styles.card, styles.profileCard, { backgroundColor: theme.colors.surfaceVariant }]}>
                <Card.Content style={styles.cardContent}>
                  <View style={[styles.iconContainer, { backgroundColor: theme.colors.primaryContainer }]}>
                    <MaterialIcons name="person" size={28} color={theme.colors.primary} />
                  </View>
                  <RNText style={[styles.cardText, { color: theme.colors.onSurface }]}>Profile</RNText>
                </Card.Content>
              </Card>
            </TouchableOpacity>

            {/* Edit Group 卡片 */}
            <TouchableOpacity
              style={styles.cardWrapper}
              onPress={() => handleCardPress('EditGroup')}
              activeOpacity={0.7}
            >
              <Card style={[styles.card, styles.editGroupCard, { backgroundColor: theme.colors.surfaceVariant }]}>
                <Card.Content style={styles.cardContent}>
                  <View style={[styles.iconContainer, { backgroundColor: theme.colors.secondaryContainer }]}>
                    <MaterialIcons name="edit" size={28} color={theme.colors.secondary} />
                  </View>
                  <RNText style={[styles.cardText, { color: theme.colors.onSurface }]}>Edit Group</RNText>
                </Card.Content>
              </Card>
            </TouchableOpacity>
          </View>

          {/* Quick Actions 區域 */}
          <Card style={[
            styles.quickActionsCard,
            {
              backgroundColor: theme.colors.surfaceVariant // 使用主題顏色
            }
          ]}>
            <Card.Content>
              {/* Quick Actions 標題 - 增強樣式和視覺層次 */}
              <View style={styles.quickActionsTitleContainer}>
                <MaterialIcons name="flash-on" size={24} color={theme.colors.primary} style={styles.quickActionsTitleIcon} />
                <Text style={[styles.quickActionsTitle, { color: theme.colors.onSurface }]}>Quick Actions</Text>
              </View>
              <View style={[styles.quickActionsDivider, { backgroundColor: theme.colors.outline }]} />

              {/* Search User 按鈕 - 優化漸變配置提升文字可讀性 */}
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleQuickAction('SearchUser')}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={[
                    theme.colors.tertiary, // 深色起始
                    theme.colors.tertiary, // 保持深色避免淡色中間區域
                    theme.dark ? '#5B21B6' : '#7C3AED' // 稍微變化的深色結束，確保對比度
                  ]}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.gradientButton}
                >
                  <View style={styles.actionButtonContent}>
                    <View style={styles.iconCircle}>
                      <MaterialIcons name="search" size={24} color="#FFFFFF" />
                    </View>
                    <RNText style={[styles.actionButtonLabel, { color: '#FFFFFF' }]}>
                      Search User
                    </RNText>
                  </View>
                </LinearGradient>
              </TouchableOpacity>

              {/* View Notifications 按鈕 - 優化漸變配置提升文字可讀性 */}
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleQuickAction('ViewNotifications')}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={[
                    theme.colors.secondary, // 深色起始
                    theme.colors.secondary, // 保持深色避免淡色中間區域
                    theme.dark ? '#0369A1' : '#0284C7' // 稍微變化的深色結束，確保對比度
                  ]}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.gradientButton}
                >
                  <View style={styles.actionButtonContent}>
                    <View style={styles.iconCircle}>
                      <MaterialIcons name="visibility" size={24} color="#FFFFFF" />
                    </View>
                    <RNText style={[styles.actionButtonLabel, { color: '#FFFFFF' }]}>
                      View My Initiated Notifications
                    </RNText>
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            </Card.Content>
          </Card>
        </ScrollView>
      </SafeAreaView>

      {/* Edit Group Modal */}
      {/* <EditGroupModal
        visible={showEditGroupModal}
        onDismiss={() => setShowEditGroupModal(false)}
        onSave={handleGroupSave}
        onDelete={handleGroupDelete}
      /> */}

      {showEditGroupModal && ( // 條件渲染 Modal，確保在不需要時完全卸載
        <EditGroupModal
          visible={showEditGroupModal}
          onDismiss={handleModalDismiss} // 使用優化的 dismiss 處理
          onSave={handleGroupSave}
          onDelete={handleGroupDelete}
        />
      )}



    </View>
  );
}

// 樣式定義 - 遵循 Material Design 原則，使用主題顏色支持 Dark Mode
const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor 通過 theme.colors.background 動態設置
  },
  statusBarBackground: {
    height: Platform.OS === 'ios' ? 20 : 0, // 優化高度：iOS 減少留白，Android 由系統處理
    // backgroundColor 通過 theme.colors.primary 動態設置
  },
  safeArea: {
    flex: 1,
  },
  header: {
    // backgroundColor 通過 theme.colors.primary 動態設置
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    height: 64, // 明確設置高度以確保與 Modal 中的 header 一致
  },
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logo: {
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  appTitle: {
    marginLeft: 12,
    fontSize: 20,
    // color 通過 theme.colors.onPrimary 動態設置
  },
  content: {
    flex: 1,
    padding: 24,
  },
  cardGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    gap: 16,
  },
  cardWrapper: {
    flex: 1,
  },
  card: {
    // backgroundColor 通過 theme.colors.surfaceVariant 動態設置
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    borderRadius: 12,
    aspectRatio: 1, // 正方形卡片
  },
  profileCard: {
    // Profile 卡片特定樣式
  },
  editGroupCard: {
    // Edit Group 卡片特定樣式
  },
  cardContent: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20, // 調整間距以匹配設計
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    // backgroundColor 通過主題顏色動態設置
  },
  cardText: {
    fontSize: 16,
    fontWeight: '800', // font-medium 對應 500
    textAlign: 'center',
    marginTop: 8,
    fontFamily: Platform.OS === 'ios' ? 'System' : undefined, // 平台特定系統字體
    lineHeight: Platform.OS === 'android' ? 20 : undefined, // Android 特定行高優化
    includeFontPadding: false, // Android 特定：移除字體內邊距以改善文字顯示
  },
  quickActionsCard: {
    // backgroundColor 通過 theme.colors.surfaceVariant 動態設置
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    borderRadius: 12,
    padding: 24,
  },
  quickActionsTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8, // 減少底部邊距，因為有分隔線
  },
  quickActionsTitleIcon: {
    marginRight: 8, // 圖標與文字間距
  },
  quickActionsTitle: {
    fontSize: 22, // 從 18px 增加到 22px 提升視覺層次
    fontWeight: '700', // 從 '600' 增加到 '700' 增強粗細
    // color 通過 theme.colors.onSurface 動態設置
    flex: 1, // 讓標題佔據剩餘空間
  },
  quickActionsDivider: {
    height: 1, // 分隔線高度
    marginBottom: 20, // 分隔線與按鈕間距
    opacity: 0.3, // 微妙的分隔效果
  },
  actionButton: {
    marginBottom: 16,
    borderRadius: 16, // 增加圓角
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    overflow: 'hidden', // 確保漸變不會超出圓角
  },
  gradientButton: {
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  iconCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.15)', // 調整透明度提升對比度
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  searchUserButton: {
    // 使用主題顏色，不需要硬編碼背景色
  },
  viewNotificationsButton: {
    // 使用主題顏色，不需要硬編碼背景色
  },
  actionButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  actionButtonLabel: {
    fontSize: 16,
    fontWeight: '600',
    // color 通過主題顏色動態設置
    flex: 1,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'sans-serif',
    ...(Platform.OS === 'android' && {
      lineHeight: 20,
      includeFontPadding: false,
    }),
  },
});
